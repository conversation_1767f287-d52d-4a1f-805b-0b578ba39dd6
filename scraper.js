import { launch } from "puppeteer";

async function runScraper() {
  const browser = await launch({
    headless: false, // Set to true for headless mode
    defaultViewport: null,
  });

  try {
    const page = await browser.newPage();
    // It's good practice to have a longer default timeout for waiting on elements
    page.setDefaultTimeout(60000);

    // Step 1: Navigate to login page
    console.log("Navigating to login page...");
    await page.goto("https://funcaoconsig.digio.com.br/WebAutorizador/Login/");

    // Step 2: Wait for manual login.
    // We wait for an element that only appears AFTER login.
    // Setting timeout to 0 means it will wait indefinitely for you to log in.
    console.log("Waiting for you to log in manually...");
    await page.waitForSelector('a.dropdown-toggle[data-toggle="dropdown"]', {
      timeout: 0,
    });
    console.log("Login detected, proceeding...");

    // Step 3: Hover over "Cadastro" dropdown to open menu
    console.log("Hovering over Cadastro dropdown...");
    await page.hover('a.dropdown-toggle[data-toggle="dropdown"]');

    // Step 4: Click "Proposta Consignado"
    // Instead of a fixed wait, we wait for the menu item to be clickable.
    console.log("Clicking Proposta Consignado...");
    await page.waitForSelector("#WFP2010_PWCDPRPS", { visible: true });
    await page.click("#WFP2010_PWCDPRPS");

    // Step 5: Wait for page load and click "Confirmar"
    console.log("Waiting for page load and clicking Confirmar...");
    await page.waitForSelector("#btnConfirmar_txt", { visible: true });
    await page.click("#btnConfirmar_txt");

    // Step 6: Select "REFINANCIAMENTO" option
    console.log("Selecting REFINANCIAMENTO...");
    // Wait for the first dropdown to be ready and select the option.
    await page.waitForSelector('select > option[value="Refinanciamento"]');
    await page.select('select', 'Refinanciamento');

    // Step 7: Wait for load and select "INSS"
    console.log("Selecting INSS...");
    
    await page.waitForSelector('select > option[value="5"]'); // Wait for the option to be populated
    const selectHandles = await page.$$("select");
    if (selectHandles.length > 1) {
      await selectHandles[1].select("5"); // Select 'INSS' from the second dropdown
    } else {
      throw new Error("Could not find the second dropdown to select 'INSS'.");
    }

    // Step 8: Click radio button
    console.log("Clicking radio button...");
    const radioButtonSelector =
      "#ctl00_Cph_UcPrp_FIJN1_JnDadosIniciais_UcDIni_UcTipoFormalizacao_rblTpFormalizacao_1";
    await page.waitForSelector(radioButtonSelector, { visible: true });
    await page.click(radioButtonSelector);

    // Step 9: Input the CPF
    console.log("Typing CPF...");
    const cpfInputSelector =
      "#ctl00_Cph_UcPrp_FIJN1_JnDadosIniciais_UcDIni_txtCPF_CAMPO";
    await page.waitForSelector(cpfInputSelector, { visible: true });

    // A more robust way to fill input fields on complex pages.
    // First, click the input to focus it and clear any existing value.
    await page.click(cpfInputSelector, { clickCount: 3 });
    await page.keyboard.press('Backspace');
    // Then, type the new value.
    await page.type(cpfInputSelector, "13504510706");
    // Finally, press Tab to ensure 'change' and other events fire correctly.
    await page.keyboard.press('Tab');
    
    // Step 10: Wait for the CPF modal and click the link
    console.log("Waiting for CPF modal to appear...");
    try {
      // First, try to wait for the specific CPF modal that should appear
      console.log("Looking for CPF modal with ID: ctl00_cph_FIJanela1_FIJanelaPanel1");

      try {
        await page.waitForSelector('#ctl00_cph_FIJanela1_FIJanelaPanel1', {
          visible: true,
          timeout: 5000
        });
        console.log("Found expected CPF modal!");

        // Look for the specific CPF link
        console.log("Looking for CPF link: #ctl00_cph_FIJanela1_FIJanelaPanel1_grvHomo_ctl02_lnkCodigo");
        await page.waitForSelector('#ctl00_cph_FIJanela1_FIJanelaPanel1_grvHomo_ctl02_lnkCodigo', {
          visible: true,
          timeout: 3000
        });

        console.log("Found CPF link! Clicking...");
        await page.click('#ctl00_cph_FIJanela1_FIJanelaPanel1_grvHomo_ctl02_lnkCodigo');
        console.log("CPF link clicked successfully!");

      } catch (specificModalError) {
        console.log("Specific CPF modal not found, checking what's actually on the page...");

        // Wait a bit more to see if any modal appears
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Debug what's actually on the page first
      const pageInfo = await page.evaluate(() => {
        return {
          url: window.location.href,
          title: document.title,
          allModals: Array.from(document.querySelectorAll('[id*="FIJanela"], [id*="Modal"], [class*="modal"]')).map(el => ({
            id: el.id,
            tagName: el.tagName,
            className: el.className,
            visible: el.offsetParent !== null,
            text: el.textContent?.substring(0, 300)
          })),
          cpfElements: Array.from(document.querySelectorAll('*')).filter(el =>
            el.textContent && el.textContent.includes('13504510706')
          ).map(el => ({
            tagName: el.tagName,
            id: el.id,
            className: el.className,
            text: el.textContent?.trim()
          })),
          allLinks: Array.from(document.querySelectorAll('a')).map(el => ({
            id: el.id,
            href: el.href,
            text: el.textContent?.trim(),
            visible: el.offsetParent !== null
          })).filter(link => link.text && link.text.includes('13504510706'))
        };
      });

      console.log("=== PAGE DEBUG INFO ===");
      console.log("Current URL:", pageInfo.url);
      console.log("Page title:", pageInfo.title);
      console.log("All modals found:", pageInfo.allModals);
      console.log("Elements containing CPF:", pageInfo.cpfElements);
      console.log("Links containing CPF:", pageInfo.allLinks);
      console.log("=======================");

      // Look for CPF links in any visible modal or on the page
      if (pageInfo.allLinks.length > 0) {
        const cpfLink = pageInfo.allLinks.find(link => link.visible);
        if (cpfLink) {
          console.log(`Found CPF link with ID: ${cpfLink.id}, clicking...`);
          await page.click(`#${cpfLink.id}`);
          console.log("CPF link clicked successfully!");
        } else {
          console.log("CPF links found but none are visible");
        }
      } else {
        // Try the original selectors as fallback
        try {
          await page.waitForSelector('tr[id*="FIJanela"]', {
            visible: true,
            timeout: 5000
          });

          console.log("Modal found! Looking for CPF link...");

          // First, try to find the specific link you mentioned
          const specificLinkExists = await page.evaluate(() => {
            const specificLink = document.querySelector('#ctl00_cph_FIJanela1_FIJanelaPanel1_grvHomo_ctl02_lnkCodigo');
            return specificLink ? {
              exists: true,
              visible: specificLink.offsetParent !== null,
              text: specificLink.textContent?.trim(),
              href: specificLink.href
            } : { exists: false };
          });

          console.log("Specific link check:", specificLinkExists);

          if (specificLinkExists.exists) {
            console.log("Found the specific CPF link! Clicking...");
            await page.click('#ctl00_cph_FIJanela1_FIJanelaPanel1_grvHomo_ctl02_lnkCodigo');
            console.log("Specific CPF link clicked successfully!");
          } else {
            // Debug all links in the modal
            const allModalLinks = await page.evaluate(() => {
              const modal = document.querySelector('tr[id*="FIJanela"]');
              if (!modal) return [];

              const links = Array.from(modal.querySelectorAll('a'));
              return links.map(link => ({
                id: link.id,
                text: link.textContent?.trim(),
                href: link.href,
                visible: link.offsetParent !== null
              }));
            });

            console.log("All links found in modal:", allModalLinks);

            // Look for any link containing the CPF
            const cpfLinkSelector = await page.evaluate(() => {
              const links = Array.from(document.querySelectorAll('a'));
              const cpfLink = links.find(link =>
                link.textContent && link.textContent.includes('13504510706')
              );
              return cpfLink ? `#${cpfLink.id}` : null;
            });

            if (cpfLinkSelector) {
              console.log(`Found CPF link: ${cpfLinkSelector}, clicking...`);
              await page.click(cpfLinkSelector);
              console.log("CPF link clicked successfully!");
            } else {
              console.log("No CPF link found in modal");
            }
          }

        } catch (modalError) {
          console.log("No modal found either");

          // If we're on a different page, it might mean the CPF was processed automatically
          if (pageInfo.url.includes('PropostaSintetizada')) {
            console.log("Seems like we're on the proposal page - CPF might have been processed automatically!");
          } else {
            throw modalError;
          }
        }
      }

      } // Close the catch block for specificModalError

    } catch (error) {
      console.log("Error in CPF modal handling:", error.message);

      // Check if we're on the proposal page anyway
      const currentUrl = page.url();
      if (currentUrl.includes('PropostaSintetizada')) {
        console.log("We're on the proposal page - continuing...");
      } else {
        throw error;
      }
    }

    console.log("Scraper completed successfully!");
    console.log("The browser will close in 10 seconds...");
    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds before closing
  } catch (error) {
    console.error("Error during scraping:", error);
  } finally {
    console.log("Closing browser...");
    await browser.close();
  }
}

runScraper();








